"""
EMBEDDING ONLY SCRIPT
This script only does the embedding step - it loads pre-saved chunks and embeds them.
Use this when you already have chunks saved and just want to embed them.
"""

from typing import List
import psycopg2
import pickle
import time
import os
import voyageai
from dotenv import load_dotenv

load_dotenv()

# Initialize Voyage AI client (uses VOYAGE_API_KEY from .env)
vo = voyageai.Client()

# --------------------------------------------------------------
# Load chunks from saved file
# --------------------------------------------------------------

# Try multiple locations for the chunks file (prioritize main feeling-good file)
POSSIBLE_CHUNK_FILES = [
    r"C:\Users\<USER>\Documents\DAVID.DEV\TOOLS\DOC PARSING\ai-cookbook\knowledge\docling\feeling-good-chunks.pkl",  # MAIN: Your feeling-good book
    r"C:\Users\<USER>\Documents\DAVID.DEV\TOOLS\DOC PARSING\ai-cookbook\knowledge\docling\saved_chunks.pkl",  # Backup of feeling-good book
    "feeling-good-chunks.pkl",  # Current directory
    "saved_chunks.pkl",  # Current directory backup
]

chunks = None
chunks_file_used = None

for chunks_file in POSSIBLE_CHUNK_FILES:
    if os.path.exists(chunks_file):
        print(f"📁 Loading chunks from: {chunks_file}")
        with open(chunks_file, "rb") as f:
            chunks = pickle.load(f)
        chunks_file_used = chunks_file
        break

if chunks is None:
    print("❌ ERROR: No saved chunks found!")
    print("Please run 'save-existing-chunks.py' first to save your chunks.")
    print("Looked for chunks in these locations:")
    for f in POSSIBLE_CHUNK_FILES:
        print(f"  - {f}")
    exit(1)

print(f"✅ Loaded {len(chunks)} chunks from {chunks_file_used}")

# --------------------------------------------------------------
# Connect to PostgreSQL with pgvector
# --------------------------------------------------------------

conn = psycopg2.connect(
    host="*************",
    database="darvis-postgres",
    user="postgres",
    password="qroinoruwob23u410841rqouUBUBUBo808",
    port="5432"
)

print("✅ Successfully connected to the remote Darvis database!")
cur = conn.cursor()

# Create pgvector extension if needed
cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

# --------------------------------------------------------------
# Generate embeddings and store in pgvector
# --------------------------------------------------------------

def get_embedding(text: str) -> List[float]:
    """Get embedding from Voyage AI API."""
    result = vo.embed([text], model="voyage-3-large", input_type="document")
    return result.embeddings[0]

print(f"🚀 Starting to embed {len(chunks)} chunks...")
start_time = time.time()

# Process and store each chunk
for i, chunk in enumerate(chunks):
    print(f"📝 Processing chunk {i+1}/{len(chunks)} - {time.time() - start_time:.1f}s elapsed")
    
    # Get embedding from Voyage AI
    embedding = get_embedding(chunk.text)
    
    # Extract metadata
    filename = chunk.meta.origin.filename if chunk.meta.origin else "feeling-good.pdf"
    title = chunk.meta.headings[0] if chunk.meta.headings else None
    
    # Insert into your therapy_knowledge_chunks table
    cur.execute("""
        INSERT INTO therapy_knowledge_chunks (
            content, embedding, chapter, section, therapy_technique, 
            emotional_themes, keywords, chunk_index, source_document
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        chunk.text,                           # content
        embedding,                            # embedding (1024 dimensions for voyage-3-large)
        title,                               # chapter (use heading as chapter)
        None,                                # section
        "CBT",                               # therapy_technique
        ["anxiety", "depression"],           # emotional_themes
        ["cognitive", "behavioral"],         # keywords
        i,                                   # chunk_index
        filename                             # source_document
    ))
    
    # Show progress every 10 chunks
    if (i + 1) % 10 == 0:
        print(f"✅ Completed {i+1}/{len(chunks)} chunks")
        conn.commit()  # Commit every 10 chunks

# Final commit and close
conn.commit()
cur.close()
conn.close()

total_time = time.time() - start_time
print(f"🎉 Successfully stored {len(chunks)} chunks with embeddings in pgvector database!")
print(f"⏱️  Total time: {total_time:.1f} seconds ({total_time/len(chunks):.2f}s per chunk)")
