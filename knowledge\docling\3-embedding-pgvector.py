from typing import List
import psycopg2
import numpy as np
from docling.chunking import HybridChunker
from docling.document_converter import DocumentConverter
from dotenv import load_dotenv
from openai import OpenAI
from utils.tokenizer import OpenAITokenizerWrapper

load_dotenv()

# Initialize OpenAI client
client = OpenAI()
tokenizer = OpenAITokenizerWrapper()
MAX_TOKENS = 8191

# --------------------------------------------------------------
# Extract and chunk the data (same as before)
# --------------------------------------------------------------

converter = DocumentConverter()
result = converter.convert("https://arxiv.org/pdf/2408.09869")

chunker = HybridChunker(
    tokenizer=tokenizer,
    max_tokens=MAX_TOKENS,
    merge_peers=True,
)

chunk_iter = chunker.chunk(dl_doc=result.document)
chunks = list(chunk_iter)

# --------------------------------------------------------------
# Connect to PostgreSQL with pgvector
# --------------------------------------------------------------

# Update these connection parameters for your database
conn = psycopg2.connect(
    host="localhost",  # Your pgvector host
    database="your_database",  # Your database name
    user="your_user",  # Your username
    password="your_password",  # Your password
    port="5432"  # Your port
)

cur = conn.cursor()

# Create table with pgvector extension (run once)
cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

# Create table for storing chunks and embeddings
cur.execute("""
    INSERT INTO therapy_knowledge_chunks (
        content, embedding, chapter, section, therapy_technique, 
        emotional_themes, keywords, chunk_index, source_document
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
""", (
    chunk.text,
    embedding,
    title,  # Use as chapter
    None,   # section
    "CBT",  # or detect from content
    ["anxiety", "depression"],  # extract from content
    ["cognitive", "behavioral"],  # extract keywords
    i,      # chunk index
    filename
))
# --------------------------------------------------------------
# Generate embeddings and store in pgvector
# --------------------------------------------------------------

def get_embedding(text: str) -> List[float]:
    """Get embedding from OpenAI API."""
    response = client.embeddings.create(
        model="text-embedding-3-large",
        input=text
    )
    return response.data[0].embedding

# Process and store each chunk
for i, chunk in enumerate(chunks):
    print(f"Processing chunk {i+1}/{len(chunks)}")
    
    # Get embedding
    embedding = get_embedding(chunk.text)
    
    # Extract metadata
    filename = chunk.meta.origin.filename if chunk.meta.origin else None
    page_numbers = [
        page_no
        for page_no in sorted(
            set(
                prov.page_no
                for item in chunk.meta.doc_items
                for prov in item.prov
            )
        )
    ] if chunk.meta.doc_items else None
    
    title = chunk.meta.headings[0] if chunk.meta.headings else None
    
    # Insert into database
    cur.execute("""
        INSERT INTO document_chunks (text, embedding, filename, page_numbers, title)
        VALUES (%s, %s, %s, %s, %s)
    """, (
        chunk.text,
        embedding,  # pgvector handles the conversion
        filename,
        page_numbers,
        title
    ))

# Commit and close
conn.commit()
cur.close()
conn.close()

print(f"Successfully stored {len(chunks)} chunks with embeddings in pgvector database!")

# --------------------------------------------------------------
# Example: Query similar chunks
# --------------------------------------------------------------

def search_similar_chunks(query_text: str, limit: int = 5):
    """Search for similar chunks using cosine similarity."""
    
    # Get query embedding
    query_embedding = get_embedding(query_text)
    
    # Connect to database
    conn = psycopg2.connect(
        host="localhost",
        database="your_database", 
        user="your_user",
        password="your_password",
        port="5432"
    )
    cur = conn.cursor()
    
    # Search using cosine similarity
    cur.execute("""
        SELECT text, filename, title, 
               1 - (embedding <=> %s) as similarity
        FROM document_chunks
        ORDER BY embedding <=> %s
        LIMIT %s
    """, (query_embedding, query_embedding, limit))
    
    results = cur.fetchall()
    cur.close()
    conn.close()
    
    return results

# Example usage:
# results = search_similar_chunks("What is machine learning?")
# for text, filename, title, similarity in results:
#     print(f"Similarity: {similarity:.3f}")
#     print(f"Title: {title}")
#     print(f"Text: {text[:200]}...")
#     print("-" * 50)
