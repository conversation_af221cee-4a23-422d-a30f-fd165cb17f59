from typing import List
import psycopg2
import pickle
import os
import time
import voyageai
from dotenv import load_dotenv

load_dotenv()

# Initialize Voyage AI client (uses VOYAGE_API_KEY from .env)
vo = voyageai.Client()

# --------------------------------------------------------------
# Load chunks from saved file OR do extraction + chunking
# --------------------------------------------------------------

CHUNKS_FILE = "saved_chunks.pkl"

if os.path.exists(CHUNKS_FILE):
    print("📁 Loading chunks from saved file...")
    with open(CHUNKS_FILE, "rb") as f:
        chunks = pickle.load(f)
    print(f"✅ Loaded {len(chunks)} chunks from {CHUNKS_FILE}")
else:
    print("🔄 No saved chunks found. Doing extraction + chunking...")
    from docling.chunking import HybridChunker
    from docling.document_converter import DocumentConverter

    converter = DocumentConverter()
    result = converter.convert(r"C:\Users\<USER>\Downloads\feeling-good.pdf")

    chunker = HybridChunker(
        max_tokens=32000,  # Voyage AI supports larger context
        merge_peers=True,
    )

    chunk_iter = chunker.chunk(dl_doc=result.document)
    chunks = list(chunk_iter)

    # Save chunks for future use
    with open(CHUNKS_FILE, "wb") as f:
        pickle.dump(chunks, f)
    print(f"💾 Saved {len(chunks)} chunks to {CHUNKS_FILE}")
    print("🔄 Next time, run this script again to skip extraction/chunking!")

# --------------------------------------------------------------
# Connect to PostgreSQL with pgvector
# --------------------------------------------------------------

conn = psycopg2.connect(
    host="*************",
    database="darvis-postgres",
    user="postgres",
    password="qroinoruwob23u410841rqouUBUBUBo808",
    port="5432"
)

print("✅ Successfully connected to the remote Darvis database!")
cur = conn.cursor()

# Create pgvector extension if needed
cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
# --------------------------------------------------------------
# Generate embeddings and store in pgvector
# --------------------------------------------------------------

def get_embedding(text: str) -> List[float]:
    """Get embedding from Voyage AI API."""
    result = vo.embed([text], model="voyage-3-large", input_type="document")
    return result.embeddings[0]

print(f"🚀 Starting to embed {len(chunks)} chunks...")
start_time = time.time()

# Process and store each chunk
for i, chunk in enumerate(chunks):
    print(f"📝 Processing chunk {i+1}/{len(chunks)} - {time.time() - start_time:.1f}s elapsed")

    # Get embedding from Voyage AI
    embedding = get_embedding(chunk.text)

    # Extract metadata
    filename = chunk.meta.origin.filename if chunk.meta.origin else "feeling-good.pdf"
    title = chunk.meta.headings[0] if chunk.meta.headings else None

    # Insert into your therapy_knowledge_chunks table
    cur.execute("""
        INSERT INTO therapy_knowledge_chunks (
            content, embedding, chapter, section, therapy_technique,
            emotional_themes, keywords, chunk_index, source_document
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        chunk.text,                           # content
        embedding,                            # embedding (1024 dimensions for voyage-3-large)
        title,                               # chapter (use heading as chapter)
        None,                                # section
        "CBT",                               # therapy_technique
        ["anxiety", "depression"],           # emotional_themes
        ["cognitive", "behavioral"],         # keywords
        i,                                   # chunk_index
        filename                             # source_document
    ))

    # Show progress every 10 chunks
    if (i + 1) % 10 == 0:
        print(f"✅ Completed {i+1}/{len(chunks)} chunks")
        conn.commit()  # Commit every 10 chunks

# Final commit and close
conn.commit()
cur.close()
conn.close()

total_time = time.time() - start_time
print(f"🎉 Successfully stored {len(chunks)} chunks with embeddings in pgvector database!")
print(f"⏱️  Total time: {total_time:.1f} seconds ({total_time/len(chunks):.2f}s per chunk)")

# --------------------------------------------------------------
# Example: Query similar chunks function
# --------------------------------------------------------------

def search_similar_chunks(query_text: str, limit: int = 5):
    """Search for similar chunks using cosine similarity."""

    # Get query embedding
    query_embedding = get_embedding(query_text)

    # Connect to database
    conn = psycopg2.connect(
        host="*************",
        database="darvis-postgres",
        user="postgres",
        password="qroinoruwob23u410841rqouUBUBUBo808",
        port="5432"
    )
    cur = conn.cursor()

    # Search using cosine similarity in your therapy table
    cur.execute("""
        SELECT content, chapter, therapy_technique,
               1 - (embedding <=> %s) as similarity
        FROM therapy_knowledge_chunks
        ORDER BY embedding <=> %s
        LIMIT %s
    """, (query_embedding, query_embedding, limit))

    results = cur.fetchall()
    cur.close()
    conn.close()

    return results

# Example usage (uncomment to test):
# results = search_similar_chunks("How to deal with anxiety?")
# for content, chapter, technique, similarity in results:
#     print(f"Similarity: {similarity:.3f}")
#     print(f"Chapter: {chapter}")
#     print(f"Technique: {technique}")
#     print(f"Content: {content[:200]}...")
#     print("-" * 50)
