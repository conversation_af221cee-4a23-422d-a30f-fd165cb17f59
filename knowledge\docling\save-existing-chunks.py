"""
SAVE EXISTING CHUNKS SCRIPT
This script recreates the chunking you already did and saves it to a file.
Run this ONCE to save your existing chunks, then use the embedding-only script.
"""

from docling.chunking import HybridChunker
from docling.document_converter import DocumentConverter
import pickle
import os

print("🔄 Recreating your previous chunking process...")

# --------------------------------------------------------------
# Recreate the EXACT same chunking you did before
# --------------------------------------------------------------

converter = DocumentConverter()
# Use the same PDF you used before
result = converter.convert(r"C:\Users\<USER>\Downloads\feeling-good.pdf")

# Use the same chunking settings you used before
chunker = HybridChunker(
    max_tokens=32000,  # Same as your 2-chunking.py
    merge_peers=True,
)

chunk_iter = chunker.chunk(dl_doc=result.document)
chunks = list(chunk_iter)

print(f"✅ Created {len(chunks)} chunks (same as before)")

# --------------------------------------------------------------
# Save chunks to multiple locations for safety
# --------------------------------------------------------------

# Save to current directory
CHUNKS_FILE = "saved_chunks.pkl"
with open(CHUNKS_FILE, "wb") as f:
    pickle.dump(chunks, f)
print(f"💾 Saved chunks to: {os.path.abspath(CHUNKS_FILE)}")

# Save to a backup location
BACKUP_DIR = r"C:\Users\<USER>\Documents\DAVID.DEV\TOOLS\DOC PARSING\ai-cookbook\knowledge\docling"
BACKUP_FILE = os.path.join(BACKUP_DIR, "feeling-good-chunks.pkl")
with open(BACKUP_FILE, "wb") as f:
    pickle.dump(chunks, f)
print(f"💾 Backup saved to: {BACKUP_FILE}")

# Save chunk info as text for inspection
INFO_FILE = os.path.join(BACKUP_DIR, "chunks-info.txt")
with open(INFO_FILE, "w", encoding="utf-8") as f:
    f.write(f"Total chunks: {len(chunks)}\n")
    f.write(f"Source: feeling-good.pdf\n")
    f.write(f"Chunking settings: max_tokens=32000, merge_peers=True\n\n")
    
    for i, chunk in enumerate(chunks[:5]):  # Show first 5 chunks
        f.write(f"=== CHUNK {i+1} ===\n")
        f.write(f"Length: {len(chunk.text)} characters\n")
        f.write(f"Heading: {chunk.meta.headings[0] if chunk.meta.headings else 'None'}\n")
        f.write(f"Preview: {chunk.text[:200]}...\n\n")
    
    if len(chunks) > 5:
        f.write(f"... and {len(chunks) - 5} more chunks\n")

print(f"📄 Chunk info saved to: {INFO_FILE}")

print("\n🎉 DONE! Your chunks are now saved. Next steps:")
print("1. Use 4-embedding-only.py to embed these saved chunks")
print("2. Or modify 3-embedding-pgvector.py to use these saved chunks")
print(f"\nChunk files created:")
print(f"  - Main: {os.path.abspath(CHUNKS_FILE)}")
print(f"  - Backup: {BACKUP_FILE}")
print(f"  - Info: {INFO_FILE}")
