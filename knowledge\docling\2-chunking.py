from docling.chunking import Hybrid<PERSON>hunker
from docling.document_converter import DocumentConverter
from dotenv import load_dotenv
# from openai import OpenAI
# from utils.tokenizer import OpenAITokenizerWrapper

load_dotenv()

# Updated for Voyage AI - much larger context length!
# Use a simple string-based tokenizer that works with Docling
MAX_TOKENS = 32000  # voyage-3-large's maximum context length (vs 8191 for OpenAI)


# --------------------------------------------------------------
# Extract the data
# --------------------------------------------------------------

converter = DocumentConverter()
# Use the technical paper first (20 pages, fast test)
# result = converter.convert("https://arxiv.org/pdf/2408.09869")

# Use your book later (700 pages, takes hours)
result = converter.convert(r"C:\Users\<USER>\Downloads\feeling-good.pdf")


# --------------------------------------------------------------
# Apply hybrid chunking
# --------------------------------------------------------------

# Use basic chunking without custom tokenizer to avoid issues
chunker = HybridChunker(
    max_tokens=MAX_TOKENS,
    merge_peers=True,
)

chunk_iter = chunker.chunk(dl_doc=result.document)
chunks = list(chunk_iter)

print(f"Created {len(chunks)} chunks from your document!")
print(f"Each chunk is optimized for Voyage AI's {MAX_TOKENS:,} token limit")

# Optional: Save chunk information for reference
print(f"\nFirst chunk preview (first 200 characters):")
if chunks:
    print(chunks[0].text[:200] + "...")

print(f"\nChunk sizes (in characters):")
for i, chunk in enumerate(chunks[:5]):  # Show first 5 chunks
    print(f"Chunk {i+1}: {len(chunk.text)} characters")
if len(chunks) > 5:
    print(f"... and {len(chunks) - 5} more chunks")
